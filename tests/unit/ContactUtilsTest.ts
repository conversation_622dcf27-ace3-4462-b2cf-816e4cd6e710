import {sortEmailObjects} from '@src/libs/ContactUtils';
import {localeCompare} from '../utils/TestHelper';

describe('ContactUtils', () => {
    describe('sortEmailObjects', () => {
        it('Should sort email objects with Expensify emails first', () => {
            const emails = [{value: '<EMAIL>'}, {value: '<EMAIL>'}, {value: '<EMAIL>'}, {value: '<EMAIL>'}];
            const sortedEmails = sortEmailObjects(emails, localeCompare);
            expect(sortedEmails).toEqual(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
        });
    });
});
