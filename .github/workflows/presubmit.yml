name: PR Presubmit Reviews

permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    types: [opened, synchronize, labeled]
  pull_request_review_comment:
    types: [created]

jobs:
  review:
    runs-on: ubuntu-latest
    if: contains(github.event.pull_request.labels.*.name, 'AI Review')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check required secrets
        run: |
          if [ -z "${{ secrets.LLM_API_KEY }}" ]; then
            echo "Error: LLM_API_KEY secret is not configured"
            exit 1
          fi

      - name: Read RULES.md
        id: read-rules
        run: |
          RULES_CONTENT=$(cat "contributingGuides/review/RULES.md")
          {
            echo "content<<EOF"
            echo "$RULES_CONTENT"
            echo "EOF"
          } >> "$GITHUB_OUTPUT"

      - name: Print RULES.md
        run: |
          echo "📋 RULES.md"
          echo "======================================"
          echo "${{ steps.read-rules.outputs.content }}"
          echo "======================================"

      - uses: aldo-expensify/ai-reviewer-hackaton@c91b349b41efdf1eeaa290716628d76068148f0b
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_MODEL: "claude-3-7-sonnet-20250219"
        with:
          style_guide_rules: ${{ steps.read-rules.outputs.content }}
